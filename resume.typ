#set document(title: "林进森的简历", author: "林进森")
#set page(margin: (x: 1.5cm, y: 2cm))
#set text(font: "Lato", size: 11pt)

#let section(title) = {
  heading(
    text(weight: "bold", size: 14pt, title)
  )
  line(length: 100%, stroke: 0.5pt + gray)
}

// 个人信息
#align(center)[
  #text(weight: "bold", size: 20pt)[林进森]
  #line(length: 0pt)
  #text(size: 14pt)[Android开发工程师]
]

#grid(
  columns: (1fr, 1fr),
  gutter: 1em,
  [
    #text(weight: "bold")[邮箱：] kesco915\@gmail.com
    
    #text(weight: "bold")[电话：] 15814094377
  ],
  [
    #text(weight: "bold")[地址：] 广东省深圳市
    
    #text(weight: "bold")[个人网站：] http://kescoode.com
    
    #text(weight: "bold")[GitHub：] http://github.com/kesco
  ]
)

// 关于
#section("关于")

Hello World! 我是林进森,一名Android开发工程师,有六年的工作经验。平时喜欢钻研学习各种技术,同时根据自己的想法去实现和维护。感谢您花时间阅读我的简历,期待能有机会和您共事。

// 工作经历
#section("工作经历")

#text(weight: "bold")[深圳市腾讯计算机系统有限公司]，客户端工程师
#text(style: "italic")[2016年10月至今 · 4年5个月]

先后于腾讯PCG事业群社交平台产品部（2016年至2019年）和IEG事业群用户平台部（2019年至今）担任客户端工程师，负责参与游戏知己SDK、手机QQ等等业务的开发和日常运营维护工作。

#text(weight: "bold")[项目经历]

#text(weight: "bold")[游戏知己SDK] #text(style: "italic")[2019年10月至今]

游戏知己是一款游戏AI机器人，拥有客服、智能推荐等等功能，已经接入多款游戏（比如王者小妲己）。个人主要负责知己SDK的Android端业务开发和性能优化。

- NB框架及其发布平台Android端SDK功能开发和维护。
- VLink SDK出海项目业务开发。
- SDK插件化改造和性能优化，比如初次Dex加载速度等等调优。
- SDK WebSocket长链接能力建设和网络栈重构。

#text(weight: "bold")[手机QQ] #text(style: "italic")[2016年10月至2019年10月]

参与Android版手Q业务需求开发、组件框架维护和性能监控。

- 主页面框架和消息页框架维护和相关需求开发，比如7.0版本底部栏Icon菜单、AIO消息文本支持部分选择等等。
- 互联分享功能组件维护和相关需求开发，比如第三方授权管理、第三方设置动态头像等等。
- 使用Lua语言编写AIO闪字ARK功能模块。
- 维护手Q SP组件和相关业务调用监控。
- Native内存监控组件开发，Debug版本监控Native内存业务用量、内存泄漏和Double Free问题。

#text(weight: "bold")[Wtlogin SDK] #text(style: "italic")[2019年1月至2019年10月]

Wtlogin SDK日常维护和需求开发。

- 注册协议改造，支持手Q注册流程改造和手表QQ。
- 登录协议改造，针对UIN下发特定加密Token，支持手Q加密SD卡目录需求。
- 支持IPV6。
- 适配Android Q。

#text(weight: "bold")[手Q互联SDK] #text(style: "italic")[2018年10月至2019年10月]

互联SDK日常维护运营和需求开发。

- 手Q 800 - 808版本互联分享接口开发，比如支持设置动态头像、分享小程序等等。
- HTTP CGI改造，从Apache HttpClient切换到HttpUrlConnection。
- 迁移构建脚本，从ANT切换到Gradle。
- 适配Android Q。
- 维护SDK文档。

#text(weight: "bold")[DOV] #text(style: "italic")[2017年4月 - 2018年6月]

参与DOV项目Android客户端开发。

- 主页面框架和切换动画。
- 拍摄模块特效文字贴纸功能。
- 使用Lua语言编写AIO闪字ARK功能模块。

#text(weight: "bold")[阿里巴巴网络技术有限公司]，无线开发工程师
#text(style: "italic")[2016年2月 - 2016年10月 · 8个月]

于阿里巴巴的1688技术部Android应用开发组担任无线开发工程师，参与手机阿里Android客户端开发以及相应的持续集成工程开发工作。

#text(weight: "bold")[项目经历]

#text(weight: "bold")[2016年手机阿里首页改造项目] #text(style: "italic")[2016年7月 - 2016年8月]

对手机阿里原来的旧首页进行重构改造。

- 参与首页的ROC组件的开发和模板渲染。
- 采用Weex做动态降级方案，接入调试Weex以及开发相应的Weex Component。
- 开发首页使用的下拉刷新以及组件间拖拽动画效果。

#text(weight: "bold")[2016年手机阿里Offer Detail改造项目] #text(style: "italic")[2016年5月 - 2016年7月]

对原先使用Hybrid Web开发的Offer Detail页面进行Native化改造。

- 参与Offer Detail页面的ROC组件的开发和模板渲染。
- 负责进货参谋部分的数据图表UI控件的开发和维护。

#text(weight: "bold")[手机阿里AliDatabinding开发与相关页面重构] #text(style: "italic")[2016年2月 - 2016年9月]

参与内部MVVM框架AliDataBinding的开发工作和对采用原MVP框架的页面进行重构。

- 参与AliDataBinding的View Sync部分开发，适配内部自定义组件以及修复相应的视图绑定Bug。
- 重构收藏夹、猜你喜欢等等页面。
- 相关页面Crash率从0.07%降到0.04%。

#text(weight: "bold")[金蝶国际软件]，Android工程师
#text(style: "italic")[2014年7月 - 2016年1月 · 1年7个月]

于金蝶移动互联网事业部担任开发工程师，参与金蝶云之家平台Android App开发工作与维护工作。

#text(weight: "bold")[项目经历]

#text(weight: "bold")[云之家部落App推送服务] #text(style: "italic")[2015年11月 - 2015年12月]

云之家部落App推送的技术预演项目，计划用于替换现有的推送服务。

- 使用Netty异步框架编写后端长链接Comet层组件和业务服务器HTTP RPC调用组件。
- 参与终端推送框架的开发，推送功能主要使用BSD Socket和C编写，构建脚本使用Cmake。
- 维护客户端推送协议以及内部RPC协议的相应文档和项目相关的持续集成脚本。

#text(weight: "bold")[云之家部落Android客户端] #text(style: "italic")[2014年8月 - 2015年12月]

金蝶云之家推出免费的企业团队协作与分享工具。

- 独立完成新的功能需求和修复应用出现的Bug。
- 重构改进应用的UI模块，比如拖拽手势模块，加载动画等等。
- 维护应用HTTP框架，跟踪修复相应的网络请求Bug。
- 编写项目Jenkin CI自动构建脚本，自动构建每日测试版和正式版的多个渠道包。开始使用Python编写，后来用Shell和Sed重构，目前完成一个渠道包的构建大约2分钟左右。
- 维护App Rest Api文档项目，使用Gitbook和Jenkin自动生成文档页面。

// 教育经历
#section("教育经历")

#text(weight: "bold")[电子信息工程]，深圳大学
#text(style: "italic")[2010年9月 - 2014年6月]

// 技能清单
#section("技能清单")

#text(weight: "bold")[终端开发]
- 熟悉Android开发，了解Google JetPack和一些流行的开源库。
- 有NDK C++开发能力，对Native层的内存监控和Crash有一定经验。
- 对Java的动态代理和Annotation Processor相关方面比较熟悉，有一定的插件开发经验。
- 有Weex\Hippy等类RN框架的开发经验，给Hippy做过优化的Pull & Request。
- 有一定Hook的经验，比如PLT Hook和JNI Hook。

#text(weight: "bold")[Web和后端开发]
- 熟悉JavaScript(ES 5)以及JS方言CofferScript，了解Avalon.js、Vue和Foundation框架。
- 熟悉Python，会使用Flask编写后台和CI脚本。

#text(weight: "bold")[Linux]
- 熟悉Linux下的C++开发环境，了解Shell脚本，能编写Cmake编译脚本和使用Vagrind追踪程序内存泄漏。
- 熟练使用Vim，有自己维护的一套Vim配置: http://github.com/kesco/kesco-vim

#text(weight: "bold")[版本管理、文档和自动化部署工具]
- 熟悉Git的日常使用流程，掌握基本Git的CLI命令，了解并在日常开发中使用Git-Flow工作流。
- 了解Jenkin CI的工作方法，对Jenkin和Gitlab之间的持续构建比较熟悉。
