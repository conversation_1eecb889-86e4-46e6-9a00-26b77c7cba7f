#set document(title: "林进森的简历", author: "林进森")
#set page(margin: (x: 1.5cm, y: 2cm))
#set text(size: 11pt)

// 章节标题样式
#let section(title) = {
  v(1.5em)
  text(
    weight: "bold",
    size: 14pt,
    fill: gray.darken(30%),
    smallcaps(title)
  )
  v(0.5em)
  line(length: 100%, stroke: 1pt + gray.lighten(60%))
  v(1em)
}

// 个人信息
#align(center)[
  #text(weight: "bold", size: 24pt)[林进森]
  #v(0.3em)
  #text(size: 16pt, fill: gray)[Android开发工程师]
]

#v(1.5em)

// 联系信息
#grid(
  columns: (auto, 1fr, auto, 1fr),
  column-gutter: 1em,
  row-gutter: 0.8em,

  // 第一行
  text(fill: gray)[📧],
  link("mailto:<EMAIL>")[kesco915\@gmail.com],
  text(fill: gray)[📱],
  link("tel:15814094377")[15814094377],

  // 第二行
  text(fill: gray)[📍],
  [广东省深圳市],
  [], [],
)

#v(1.5em)
#line(length: 100%, stroke: 0.5pt + gray.lighten(60%))
#v(1em)

// 社交链接
#grid(
  columns: (auto, 1fr),
  row-gutter: 0.5em,

  text(fill: orange)[🌐],
  link("http://kescoode.com")[http://kescoode.com],

  text(fill: gray.darken(20%))[⚡],
  link("http://github.com/kesco")[http://github.com/kesco],
)

#section("关于")

Hello World! 我是林进森,一名Android开发工程师,有六年的工作经验。平时喜欢钻研学习各种技术,同时根据自己的想法去实现和维护。感谢您花时间阅读我的简历,期待能有机会和您共事。

#section("工作经历")

#v(1em)
#text(weight: "bold", size: 12pt)[深圳市腾讯计算机系统有限公司]，客户端工程师
#v(0.3em)
#text(style: "italic", fill: gray, size: 10pt)[2016年10月至今 · 4年5个月]
#v(0.5em)

先后于腾讯PCG事业群社交平台产品部（2016年至2019年）和IEG事业群用户平台部（2019年至今）担任客户端工程师，负责参与游戏知己SDK、手机QQ等等业务的开发和日常运营维护工作。

#v(0.8em)
#text(weight: "bold")[项目经历]

#v(0.8em)
#text(weight: "bold")[游戏知己SDK] #text(style: "italic", fill: gray, size: 10pt)[2019年10月至今]
#v(0.3em)
游戏知己是一款游戏AI机器人，拥有客服、智能推荐等等功能，已经接入多款游戏（比如王者小妲己）。个人主要负责知己SDK的Android端业务开发和性能优化。
#v(0.3em)
- NB框架及其发布平台Android端SDK功能开发和维护。
- VLink SDK出海项目业务开发。
- SDK插件化改造和性能优化，比如初次Dex加载速度等等调优。
- SDK WebSocket长链接能力建设和网络栈重构。

#v(0.8em)
#text(weight: "bold")[手机QQ] #text(style: "italic", fill: gray, size: 10pt)[2016年10月至2019年10月]
#v(0.3em)
参与Android版手Q业务需求开发、组件框架维护和性能监控。
#v(0.3em)
- 主页面框架和消息页框架维护和相关需求开发，比如7.0版本底部栏Icon菜单、AIO消息文本支持部分选择等等。
- 互联分享功能组件维护和相关需求开发，比如第三方授权管理、第三方设置动态头像等等。
- 使用Lua语言编写AIO闪字ARK功能模块。
- 维护手Q SP组件和相关业务调用监控。
- Native内存监控组件开发，Debug版本监控Native内存业务用量、内存泄漏和Double Free问题。

#v(0.8em)
#text(weight: "bold")[Wtlogin SDK] #text(style: "italic", fill: gray, size: 10pt)[2019年1月至2019年10月]
#v(0.3em)
Wtlogin SDK日常维护和需求开发。
#v(0.3em)
- 注册协议改造，支持手Q注册流程改造和手表QQ。
- 登录协议改造，针对UIN下发特定加密Token，支持手Q加密SD卡目录需求。
- 支持IPV6。
- 适配Android Q。

#v(1.5em)
#text(weight: "bold", size: 12pt)[阿里巴巴网络技术有限公司]，无线开发工程师
#v(0.3em)
#text(style: "italic", fill: gray, size: 10pt)[2016年2月 - 2016年10月 · 8个月]
#v(0.5em)

于阿里巴巴的1688技术部Android应用开发组担任无线开发工程师，参与手机阿里Android客户端开发以及相应的持续集成工程开发工作。

#v(1.5em)
#text(weight: "bold", size: 12pt)[金蝶国际软件]，Android工程师
#v(0.3em)
#text(style: "italic", fill: gray, size: 10pt)[2014年7月 - 2016年1月 · 1年7个月]
#v(0.5em)

于金蝶移动互联网事业部担任开发工程师，参与金蝶云之家平台Android App开发工作与维护工作。

#section("教育经历")

#v(1em)
#text(weight: "bold", size: 12pt)[电子信息工程]，深圳大学
#v(0.3em)
#text(style: "italic", fill: gray, size: 10pt)[2010年9月 - 2014年6月]

#section("技能清单")

#v(1em)
#text(weight: "bold")[终端开发]
- 熟悉Android开发，了解Google JetPack和一些流行的开源库。
- 有NDK C++开发能力，对Native层的内存监控和Crash有一定经验。
- 对Java的动态代理和Annotation Processor相关方面比较熟悉，有一定的插件开发经验。
- 有Weex\\Hippy等类RN框架的开发经验，给Hippy做过优化的Pull & Request。
- 有一定Hook的经验，比如PLT Hook和JNI Hook。

#v(0.8em)
#text(weight: "bold")[Web和后端开发]
- 熟悉JavaScript(ES 5)以及JS方言CofferScript，了解Avalon.js、Vue和Foundation框架。
- 熟悉Python，会使用Flask编写后台和CI脚本。

#v(0.8em)
#text(weight: "bold")[Linux]
- 熟悉Linux下的C++开发环境，了解Shell脚本，能编写Cmake编译脚本和使用Vagrind追踪程序内存泄漏。
- 熟练使用Vim，有自己维护的一套Vim配置: http://github.com/kesco/kesco-vim

#v(0.8em)
#text(weight: "bold")[版本管理、文档和自动化部署工具]
- 熟悉Git的日常使用流程，掌握基本Git的CLI命令，了解并在日常开发中使用Git-Flow工作流。
- 了解Jenkin CI的工作方法，对Jenkin和Gitlab之间的持续构建比较熟悉。
