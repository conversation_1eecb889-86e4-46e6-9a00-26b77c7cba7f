<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>林进森的简历</title>
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.2.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css">
  <link href='https://fonts.googleapis.com/css?family=Lato:400,500,600,700|Open+Sans:400,500,600,700' rel='stylesheet' type='text/css'>
  <link rel="stylesheet" href="resume.css">
</head>

<body itemscope="" itemtype="http://schema.org/Person">
  <div class="container">
    <section class="row">
      <div class="card-wrapper">
        <div class="card">
          <div class="profile-card">
            <!-- <div class="profile-pic">
              <img class="media-object img-circle center-block" data-src="holder.js/100x100" alt="林进森" src="http://7xlch0.com1.z0.glb.clouddn.com/pic.jpg" itemprop="image">
            </div> -->
            <div class="contact-details">
              <div class="name-and-profession">
                <h3 class="text-bolder name" itemprop="name">林进森</h3>
                <h5 class="text-muted" itemprop="jobTitle">Android开发工程师</h5>
              </div>

              <div class="detail">
                <span class="icon">
                  <i class="fa fa-lg fa-envelope"></i>
                </span>

                <span class="info">
                  <a href="mailto:<EMAIL>" class="link-disguise" itemprop="email"><EMAIL></a>
                </span>
              </div>

              <div class="detail">
                <span class="icon">
                  <i class="fa fa-lg fa-phone"></i>
                </span>

                <a href="tel://15814094377" class="link-disguise info" itemprop="telephone">
                  15814094377
                </a>
              </div>

              <div class="detail">
                <span class="icon">
                  <i class="fa fa-lg fa-map-marker"></i>
                </span>

                <span class="info">
                  广东省深圳市
                </span>
              </div>
            </div>
          </div>
          <hr>

          <div class="social-links">
            <a class="social-link" href="http://kescoode.com" target="_blank">
              <span class="fa fa-rss fa-2x"></span> 
              <span class="social-link-text">http://kescoode.com</span>
            </a>
            <a class="social-link" href="http://github.com/kesco" target="_blank">
              <span class="fa fa-github fa-2x"></span> 
              <span class="social-link-text">http://github.com/kesco</span>
            </a>
          </div>
        </div>
      </div>
    </section>

    <section class="row">
      <div class="card-wrapper">
        <div class="card background-card">
          <h4 id="about"> <span class="fa fa-lg fa-user"></span> <span class="title">关于</span> </h4>
          <div class="card-nested" itemprop="description">
            <p>
              Hello World! 我是林进森,一名Android开发工程师,有六年的工作经验。平时喜欢钻研学习各种技术,同时根据自己的想法去实现和维护。感谢您花时间阅读我的简历,期待能有机会和您共事。
            </p>
          </div>

          <h4 id="work-experience"> <span class="fa fa-lg fa-pencil-square-o"></span> <span class="title">工作经历</span> </h4>
          <ul class="list-unstyled">
            <li class="card-nested">
              <div class="content has-sidebar">
                <p class="clear-margin-sm">
                  <strong><a href="https://im.qq.com/mobileqq/" target="_blank">深圳市腾讯计算机系统有限公司</a></strong>，客户端工程师
                </p>
                <p class="text-muted">
                  <small>
                    <span class="space-right">2016年10月至今</span>
                    <span> <i class="fa fa-clock-o icon-left"></i>4年5个月</span> 
                  </small>
                </p>
                <p>先后于腾讯PCG事业群社交平台产品部（2016年至2019年）和IEG事业群用户平台部（2019年至今）担任客户端工程师，负责参与游戏知己SDK、手机QQ等等业务的开发和日常运营维护工作。</p>
                <p><strong>项目经历</strong></p>
                <p class="clear-margin-sm">
                  <strong>游戏知己SDK</strong>，
                  <small>
                      <span> <i class="fa fa-clock-o icon-left"></i>2019年10月至今</span> 
                  </small>
                </p>
                <p>游戏知己是一款游戏AI机器人，拥有客服、智能推荐等等功能，已经接入多款游戏（比如王者小妲己）。个人主要负责知己SDK的Android端业务开发和性能优化。</p>
                <ul>
                  <li>NB框架及其发布平台Android端SDK功能开发和维护。</li>
                  <li>VLink SDK出海项目业务开发。</li>
                  <li>SDK插件化改造和性能优化，比如初次Dex加载速度等等调优。</li>
                  <li>SDK WebSocket长链接能力建设和网络栈重构。</li>
                </ul>
                <p class="clear-margin-sm">
                  <strong>手机QQ</strong>，
                  <small>
                      <span> <i class="fa fa-clock-o icon-left"></i>2016年10月至2019年10月</span> 
                  </small>
                </p>
                <p>参与Android版手Q业务需求开发、组件框架维护和性能监控。</p>
                <ul>
                  <li>主页面框架和消息页框架维护和相关需求开发，比如7.0版本底部栏Icon菜单、AIO消息文本支持部分选择等等。</li>
                  <li>互联分享功能组件维护和相关需求开发，比如第三方授权管理、第三方设置动态头像等等。</li>
                  <li>使用Lua语言编写AIO闪字ARK功能模块。</li>
                  <li>维护手Q SP组件和相关业务调用监控。</li>
                  <li>Native内存监控组件开发，Debug版本监控Native内存业务用量、内存泄漏和Double Free问题。</li>
                </ul>
                <p class="clear-margin-sm">
                  <strong>Wtlogin SDK</strong>，
                  <small>
                      <span> <i class="fa fa-clock-o icon-left"></i>2019年1月至2019年10月</span> 
                  </small>
                </p>
                <p>Wtlogin SDK日常维护和需求开发。</p>
                <ul>
                  <li>注册协议改造，支持手Q注册流程改造和手表QQ。</li>
                  <li>登录协议改造，针对UIN下发特定加密Token，支持手Q加密SD卡目录需求。</li>
                  <li>支持IPV6。</li>
                  <li>适配Android Q。</li>
                </ul>
                <p class="clear-margin-sm">
                  <strong>手Q互联SDK</strong>，
                  <small>
                      <span> <i class="fa fa-clock-o icon-left"></i>2018年10月至2019年10月</span> 
                  </small>
                </p>
                <p>互联SDK日常维护运营和需求开发。</p>
                <ul>
                  <li>手Q 800 - 808版本互联分享接口开发，比如支持设置动态头像、分享小程序等等。</li>
                  <li>HTTP CGI改造，从Apache HttpClient切换到HttpUrlConnection。</li>
                  <li>迁移构建脚本，从ANT切换到Gradle。</li>
                  <li>适配Android Q。</li>
                  <li>维护SDK文档。</li>
                </ul>  
                <p class="clear-margin-sm">
                  <strong>DOV</strong>，
                  <small>
                      <span> <i class="fa fa-clock-o icon-left"></i>2017年4月 - 2018年6月</span> 
                  </small>
                </p>
                <p>参与DOV项目Android客户端开发。</p>
                <ul>
                  <li>主页面框架和切换动画。</li>
                  <li>拍摄模块特效文字贴纸功能。</li>
                  <li>使用Lua语言编写AIO闪字ARK功能模块。</li>
                </ul>    
              </div>
            </li>
            <li class="card-nested">
              <div class="content has-sidebar">
                <p class="clear-margin-sm">
                  <strong><a href="https://www.1688.com/" target="_blank">阿里巴巴网络技术有限公司</a></strong>，无线开发工程师
                </p>
                <p class="text-muted">
                  <small>
                    <span class="space-right">2016年2月 - 2016年10月</span>
                    <span> <i class="fa fa-clock-o icon-left"></i>8个月</span> 
                  </small>
                </p>
                <p>于阿里巴巴的1688技术部Android应用开发组担任无线开发工程师，参与手机阿里Android客户端开发以及相应的持续集成工程开发工作。</p>
                <p><strong>项目经历</strong></p>
                  <p class="clear-margin-sm">
                    <strong>2016年手机阿里首页改造项目</strong>，
                    <small>
                        <span> <i class="fa fa-clock-o icon-left"></i>2016年7月 - 2016年8月</span> 
                    </small>
                  </p>
                  <p>对手机阿里原来的旧首页进行重构改造。</p>
                  <ul>
                    <li>参与首页的ROC组件的开发和模板渲染。</li>
                    <li>采用Weex做动态降级方案，接入调试Weex以及开发相应的Weex Component。</li>
                    <li>开发首页使用的下拉刷新以及组件间拖拽动画效果。 </li>
                  </ul>
                  <p class="clear-margin-sm">
                    <strong>2016年手机阿里Offer Detail改造项目</strong>，
                    <small>
                        <span> <i class="fa fa-clock-o icon-left"></i>2016年5月 - 2016年7月</span> 
                    </small>
                  </p>
                  <p>对原先使用Hybrid Web开发的Offer Detail页面进行Native化改造。</p>
                  <ul>
                    <li>参与Offer Detail页面的ROC组件的开发和模板渲染。</li>
                    <li>负责进货参谋部分的数据图表UI控件的开发和维护。</li>
                  </ul>
                  <p class="clear-margin-sm">
                    <strong>手机阿里AliDatabinding开发与相关页面重构</strong>，
                    <small>
                        <span> <i class="fa fa-clock-o icon-left"></i>2016年2月 - 2016年9月</span> 
                    </small>
                  </p>
                  <p>参与内部MVVM框架AliDataBinding的开发工作和对采用原MVP框架的页面进行重构。</p>
                  <ul>
                    <li>参与AliDataBinding的View Sync部分开发，适配内部自定义组件以及修复相应的视图绑定Bug。</li>
                    <li>重构收藏夹、猜你喜欢等等页面。</li>
                    <li>相关页面Crash率从0.07%降到0.04%。</li>
                  </ul>      
              </div>
            </li>

            <li class="card-nested">
              <div class="content has-sidebar">
                  <p class="clear-margin-sm">
                      <strong><a href="http://www.kdweibo.com" target="_blank">金蝶国际软件</a></strong>,Android工程师
                  </p>
                  <p class="text-muted">
                      <small>
                      <span class="space-right">2014年7月 - 2016年1月</span>
                      <span> <i class="fa fa-clock-o icon-left"></i>1年7个月</span>
                  </small>
                  </p>
                  <p>于金蝶移动互联网事业部担任开发工程师，参与金蝶云之家平台Android App开发工作与维护工作。</p>
                  <p><strong>项目经历</strong></p>
                  <p class="clear-margin-sm">
                    <strong>云之家部落App推送服务</strong>，
                    <small>
                        <span> <i class="fa fa-clock-o icon-left"></i>2015年11月 - 2015年12月</span> 
                    </small>
                  </p>
                  <p>云之家部落App推送的技术预演项目，计划用于替换现有的推送服务。</p>
                  <ul>
                    <li>使用Netty异步框架编写后端长链接Comet层组件和业务服务器HTTP RPC调用组件。</li>
                    <li>参与终端推送框架的开发，推送功能主要使用BSD Socket和C编写，构建脚本使用Cmake。</li>
                    <li>维护客户端推送协议以及内部RPC协议的相应文档和项目相关的持续集成脚本。</li>
                  </ul>
                  <p class="clear-margin-sm">
                      <strong>云之家部落Android客户端</strong>，
                      <small>
                          <span> <i class="fa fa-clock-o icon-left"></i>2014年8月 - 2015年12月</span> 
                      </small>
                  </p>
                  <p>金蝶云之家推出免费的企业团队协作与分享工具。</p>
                  <ul>
                    <li>独立完成新的功能需求和修复应用出现的Bug。</li>
                    <li>重构改进应用的UI模块，比如拖拽手势模块，加载动画等等。</li>
                    <li>维护应用HTTP框架，跟踪修复相应的网络请求Bug。</li>
                    <li>编写项目Jenkin CI自动构建脚本，自动构建每日测试版和正式版的多个渠道包。开始使用Python编写，后来用Shell和Sed重构，目前完成一个渠道包的构建大约2分钟左右。</li>
                    <li>维护App Rest Api文档项目，使用Gitbook和Jenkin自动生成文档页面。</li>
                  </ul>
              </div>
          </li>
          </ul>

          <h4 id="education"><span class="fa fa-lg fa-mortar-board"></span> <span class="title">教育经历</span></h4>
          <ul class="list-unstyled">
            <li class="card-nested">
              <div class="content has-sidebar">
                <p class="clear-margin-sm">
                  <strong>电子信息工程</strong>,&nbsp; 深圳大学
                </p>
                <p class="text-muted">
                  <small>2010年9月 - 2014年6月</small>
                </p>
              </div>
            </li>
          </ul>

          <h4 id="skills"><span class="fa fa-lg fa-code"></span> <span class="title">技能清单</span></h4>
          <ul class="list-unstyled">
            <li class="card-nested">
              <p><strong>终端开发</strong></p>
              <div class="space-top labels">
                <ul>
                  <li>熟悉Android开发，了解Google JetPack和一些流行的开源库。</li>
                  <li>有NDK C++开发能力，对Native层的内存监控和Crash有一定经验。</li>
                  <li>对Java的动态代理和Annotation Processor相关方面比较熟悉，有一定的插件开发经验。</li>
                  <li>有Weex\Hippy等类RN框架的开发经验，给Hippy做过优化的Pull & Request。</li>
                  <li>有一定Hook的经验，比如PLT Hook和JNI Hook。</li>
                </ul>
              </div>
            </li>
            <li class="card-nested">
              <p><strong>Web和后端开发</strong></p>
              <div class="space-top labels">
                <ul>
                  <li>熟悉JavaScript(ES 5)以及JS方言CofferScript，了解Avalon.js、Vue和Foundation框架。</li>
                  <li>熟悉Python，会使用Flask编写后台和CI脚本。</li>
                </ul>
              </div>
            </li>
            <li class="card-nested">
              <p><strong>Linux</strong></p>
              <div class="space-top labels">
                <ul>
                  <li>熟悉Linux下的C++开发环境，了解Shell脚本，能编写Cmake编译脚本和使用Vagrind追踪程序内存泄漏。 </li>
                  <li>熟练使用Vim，有自己维护的一套Vim配置: http://github.com/kesco/kesco-vim</li>
                </ul>
              </div>
            </li>
            <li class="card-nested">
              <p><strong>版本管理、文档和自动化部署工具</strong></p>
              <div class="space-top labels">
                <ul>
                  <li>熟悉Git的日常使用流程，掌握基本Git的CLI命令，了解并在日常开发中使用Git-Flow工作流。</li>
                  <li>了解Jenkin CI的工作方法，对Jenkin和Gitlab之间的持续构建比较熟悉。</li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
        </div>
      </section>
    </div>
</body>
</html>