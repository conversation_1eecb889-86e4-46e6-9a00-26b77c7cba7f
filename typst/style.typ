#let draw_font_icon(unicode, color: rgb("#888"), size: 1em) = {
  text(
    font: "Font Awesome 6 Free Solid",
    weight: "black",
    size: size,
    fill: color,
    unicode,
  )
}

#let resume(title, author, margin, body) = {
  set document(title: title, author: author, keywords: "resume, cv")

  set page(paper: "a4", margin: margin)
  set text(font: "LXGW Bright", size: 11pt, fill: rgb("#333333"))

  // 二级标题下加一条横线
  show heading.where(level: 2): it => stack(
    v(1.2em),
    text(
      weight: "bold",
      size: 14pt,
      fill: rgb("#777777"),
      it
    ),
    v(1em),
    line(length: 100%, stroke: 1pt + rgb("#eeeeee")),
    v(1em),
  )

  body
}
